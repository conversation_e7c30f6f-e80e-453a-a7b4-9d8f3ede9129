#/** @file
# * Copyright (c) 2020, Arm Limited or its affiliates. All rights reserved.
# * SPDX-License-Identifier : Apache-2.0
# *
# * Licensed under the Apache License, Version 2.0 (the "License");
# * you may not use this file except in compliance with the License.
# * You may obtain a copy of the License at
# *
# *  http://www.apache.org/licenses/LICENSE-2.0
# *
# * Unless required by applicable law or agreed to in writing, software
# * distributed under the License is distributed on an "AS IS" BASIS,
# * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# * See the License for the specific language governing permissions and
# * limitations under the License.
#**/

if(${SUITE} STREQUAL "INITIAL_ATTESTATION")
set(PSA_QCBOR_GIT_REPO_LINK		https://github.com/laurencelundblade/QCBOR.git)
set(PSA_QCBOR_GIT_REPO_TAG              42272e466a8472948bf8fca076d113b81b99f0e0)
endif()
