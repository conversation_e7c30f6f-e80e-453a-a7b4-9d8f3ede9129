/** @file
 * Copyright (c) 2018-2024, Arm Limited or its affiliates. All rights reserved.
 * SPDX-License-Identifier : Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
**/

#ifndef _VAL_ENTRY_H_
#define _VAL_ENTRY_H_

#include "val_framework.h"

#define PSA_ACS_MAJOR_VER    1
#define PSA_ACS_MINOR_VER    7

/**
    @brief    - PSA Test Suite C main function, does VAL init and calls test dispatcher
    @param    - None
    @return   - int32_t
**/
extern int32_t val_entry(void);
#endif
