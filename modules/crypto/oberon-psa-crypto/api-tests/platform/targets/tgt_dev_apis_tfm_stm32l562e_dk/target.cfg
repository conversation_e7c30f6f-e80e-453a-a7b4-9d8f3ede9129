///** @file
// * Copyright (c) 2019, Arm Limited or its affiliates. All rights reserved.
// * SPDX-License-Identifier : Apache-2.0
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *  http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
//**/

// UART device info
uart.num=1;
uart.0.base = 0x40013800; // UART1_NS
uart.0.size = 0xFFF;
uart.0.intr_id = 0xFF;
uart.0.permission = TYPE_READ_WRITE;

// Watchdog device info
watchdog.num = 1;
watchdog.0.base = 0x40002C00;
watchdog.0.size = 0xFFF;
watchdog.0.intr_id = 0xFF;
watchdog.0.permission = TYPE_READ_WRITE;
watchdog.0.num_of_tick_per_micro_sec = 0x3;         //(sys_feq/1000000)
watchdog.0.timeout_in_micro_sec_low = 0xF4240;      //1.0  sec :  1 * 1000 * 1000
watchdog.0.timeout_in_micro_sec_medium = 0x1E8480;  //2.0  sec :  2 * 1000 * 1000
watchdog.0.timeout_in_micro_sec_high = 0x4C4B40;    //5.0  sec :  5 * 1000 * 1000
watchdog.0.timeout_in_micro_sec_crypto = 0x1312D00; //18.0 sec : 18 * 1000 * 1000

// Range of  Non-volatile memory to preserve data over reset. Ex, NVRAM and FLASH
// 0x40 bytes
nvmem.num =1;
nvmem.0.start = 0x20000000;
nvmem.0.end = 0x20000fff;
nvmem.0.permission = TYPE_READ_WRITE;

// Miscellaneous - Test scatter info
dut.num = 1;

// Start address of 12KB NS memory for test ELF
dut.0.ns_test_addr = 0x28110000;

// Start address of combine_test_binary in memory. Memory can be main memory or secondary memory.
// Size of combine_test_binary = Summation of size of each test ELF file.
dut.0.ns_start_addr_of_combine_test_binary = 0x28120000;

// Is combine_test_binary available in RAM?
dut.0.combine_test_binary_in_ram = AVAILABLE;

dut.0.implemented_psa_firmware_isolation_level = LEVEL1;
