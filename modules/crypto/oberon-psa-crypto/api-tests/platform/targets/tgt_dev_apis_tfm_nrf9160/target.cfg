///** @file
// * Copyright (c) 2019-2023, Arm Limited or its affiliates. All rights reserved.
// * SPDX-License-Identifier : Apache-2.0
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *  http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
//**/

// UART device info
uart.num=1;
uart.0.base = 0; // Unused value

// Watchdog device info
watchdog.num = 1;
watchdog.0.base = 0x40018000;
watchdog.0.num_of_tick_per_micro_sec = 1;
// The same value should be used for all timeout durations, as the nRF91
// WDT cannot be reconfigured once it has been started.
watchdog.0.timeout_in_micro_sec_low    = 500000000; // 500 secs
watchdog.0.timeout_in_micro_sec_medium = 500000000;
watchdog.0.timeout_in_micro_sec_high   = 500000000;
watchdog.0.timeout_in_micro_sec_crypto = 500000000;

// Range of 1KB Non-volatile memory to preserve data over reset. Ex, NVRAM and FLASH
nvmem.num =1;
nvmem.0.start = 0; // Unused value;
nvmem.0.end = 0x3ff;
