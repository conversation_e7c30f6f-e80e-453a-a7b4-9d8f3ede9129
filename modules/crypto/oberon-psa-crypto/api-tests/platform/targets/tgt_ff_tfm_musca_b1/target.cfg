///** @file
// * Copyright (c) 2020, Arm Limited or its affiliates. All rights reserved.
// * SPDX-License-Identifier : Apache-2.0
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *  http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
//**/

// UART device info
uart.num=1;
uart.0.base = 0x40106000; // MUSCA_B1_UART1_NS_BASE
uart.0.size = 0xFFF;
uart.0.intr_id = 0xFF;
uart.0.permission = TYPE_READ_WRITE;

// Watchdog device info
watchdog.num = 1;
watchdog.0.base = 0x50081000; // MUSCA_B1_CMSDK_WATCHDOG_S_BASE
watchdog.0.size = 0xFFF;
watchdog.0.intr_id = 0xFF;
watchdog.0.permission = TYPE_READ_WRITE;
watchdog.0.num_of_tick_per_micro_sec = 0x3;          //(sys_feq/1000000)
watchdog.0.timeout_in_micro_sec_low = 20000000;      //20.0  sec :  20 * 1000 * 1000
watchdog.0.timeout_in_micro_sec_medium = 20000000;   //20.0  sec :  20 * 1000 * 1000
watchdog.0.timeout_in_micro_sec_high = 20000000;     //20.0  sec :  20 * 1000 * 1000
watchdog.0.timeout_in_micro_sec_crypto = 20000000;   //20.0 sec :   20 * 1000 * 1000

// Range of 1KB Non-volatile memory to preserve data over reset. Ex, NVRAM and FLASH
nvmem.num =1;
nvmem.0.start = 0x3003F800;
nvmem.0.end = 0x3003FBFF;
nvmem.0.permission = TYPE_READ_WRITE;

// ###################################################################
// Following Target configuration parameters are required for IPC tests
// only. Avoid updating them if you are running dev_apis tests.
// ###################################################################

// Assign free memory range for isolation testing. Choose the addresses
// for these memory regions such that it follows below condition:
// nspe_mmio.0.start < server_partition_mmio.0.start < driver_partition_mmio.0.start.
nspe_mmio.num=1;
nspe_mmio.0.start = 0x00200F00;
nspe_mmio.0.end = 0x00200F1F;
nspe_mmio.0.permission = TYPE_READ_WRITE;

server_partition_mmio.num=1;
server_partition_mmio.0.start = 0x3003FC00;
server_partition_mmio.0.end = 0x3003FD00;
server_partition_mmio.0.permission = TYPE_READ_WRITE;

driver_partition_mmio.num=1;
driver_partition_mmio.0.start = 0x3003FE00;
driver_partition_mmio.0.end = 0x3003FF00;
driver_partition_mmio.0.permission = TYPE_READ_WRITE;
