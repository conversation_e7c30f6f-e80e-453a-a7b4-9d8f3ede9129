///** @file
// * Copyright (c) 2024, Arm Limited or its affiliates. All rights reserved.
// * SPDX-License-Identifier : Apache-2.0
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *  http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
//**/

// UART device info
uart.num=20;
uart.20.base = 0; // Unused value

// Watchdog device info

// The nRF instance that is appropriate to use on 54L non-secure is
// NRF_WDT31, but 31 breaks the test infrastructure so we pretend we are
// using the non-existent instance 4 instead.

watchdog.num = 4;
watchdog.4.base = 0x40109000;  // Unused value
watchdog.4.num_of_tick_per_micro_sec = 1;  // Unused value

watchdog.4.timeout_in_micro_sec_low    = 500000000; // Unused value
watchdog.4.timeout_in_micro_sec_medium = 500000000; // Unused value
watchdog.4.timeout_in_micro_sec_high   = 500000000; // Unused value
watchdog.4.timeout_in_micro_sec_crypto = 500000000; // Unused value

// Range of 1KB Non-volatile memory to preserve data over reset. Ex, NVRAM and FLASH
nvmem.num =1;
nvmem.0.start = 0; // Unused value;
nvmem.0.end = 0x3ff;
