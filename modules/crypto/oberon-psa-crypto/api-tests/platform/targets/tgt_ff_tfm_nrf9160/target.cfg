///** @file
// * Copyright (c) 2019-2020, Arm Limited or its affiliates. All rights reserved.
// * SPDX-License-Identifier : Apache-2.0
// *
// * Copyright (c) 2021 Nordic Semiconductor ASA.
// * SPDX-License-Identifier : Apache-2.0
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *  http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
//**/

// UART device info
uart.num=1;
uart.0.base = 0; // Unused value

// Watchdog device info
watchdog.num = 1;
watchdog.0.base = 0x50018000;
watchdog.0.num_of_tick_per_micro_sec = 1;
// The same value should be used for all timeout durations, as the nRF91
// WDT cannot be reconfigured once it has been started.
watchdog.0.timeout_in_micro_sec_low = 18000000; // 18 secs
watchdog.0.timeout_in_micro_sec_medium = 18000000;  // 18 secs
watchdog.0.timeout_in_micro_sec_high = 18000000; // 18 secs
watchdog.0.timeout_in_micro_sec_crypto = 18000000; // 18 secs

// Range of 1KB Non-volatile memory to preserve data over reset. Ex, NVRAM and FLASH
nvmem.num =1;
nvmem.0.start = 0; // Unused value
nvmem.0.end = 0x3ff;

// ###################################################################
// Following Target configuration parameters are required for IPC tests
// only. Avoid updating them if you are running dev_apis tests.
// ###################################################################

// Assign free memory range for isolation testing. Choose the addresses
// for these memory regions such that it follows below condition:
// nspe_mmio.0.start < server_partition_mmio.0.start < driver_partition_mmio.0.start.
nspe_mmio.num=1;
nspe_mmio.0.start = 0x2003DF00;
nspe_mmio.0.end = 0x2003E000;
nspe_mmio.0.permission = TYPE_READ_WRITE;

server_partition_mmio.num=1;
server_partition_mmio.0.start = 0x2003E000;
server_partition_mmio.0.end = 0x2003E100;
server_partition_mmio.0.permission = TYPE_READ_WRITE;

driver_partition_mmio.num=1;
driver_partition_mmio.0.start = 0x2003E100;
driver_partition_mmio.0.end = 0x2003E200;
driver_partition_mmio.0.permission = TYPE_READ_WRITE;
