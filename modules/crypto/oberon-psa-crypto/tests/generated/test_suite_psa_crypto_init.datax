Create NV seed file
0

PSA init/deinit
1:int:2

PSA deinit without init
2:int:0

PSA deinit twice
2:int:1

PSA threaded init checks
3:int:100

No random without init
4:int:0

No key slot access without init
5:int:0

No random after deinit
4:int:1

No key slot access after deinit
5:int:1

Custom entropy sources: all standard
6:int:0x0000ffff:exp:0

Custom entropy sources: none
depends_on:0
6:int:0:exp:1

Fake entropy: never returns anything
7:exp:2:int:0:int:0:int:0:int:0:exp:1

Fake entropy: less than the block size
7:exp:2:exp:3:int:-1:int:-1:int:-1:exp:1

Fake entropy: not enough for a nonce
depends_on:1
7:exp:2:exp:4:int:-1:int:-1:int:-1:exp:1

Fake entropy: one block eventually
depends_on:2
7:exp:2:int:0:int:0:int:0:exp:2:exp:0

Fake entropy: one block in two steps
depends_on:2
7:exp:2:exp:3:int:1:int:-1:int:-1:exp:0

Fake entropy: more than one block in two steps
depends_on:2
7:exp:2:exp:3:exp:3:int:-1:int:-1:exp:0

Fake entropy: two blocks eventually
7:exp:2:int:0:exp:2:int:0:exp:2:exp:0

NV seed only: less than minimum
8:exp:5:exp:1

NV seed only: less than one block
8:exp:3:exp:1

NV seed only: just enough
8:exp:6:exp:0

Recreate NV seed file
0

