PSA PAKE: uninitialized access to psa_pake_operation_t
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:5:exp:6

PSA PAKE: invalid alg
depends_on:0:1:2
0:exp:4:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:7:exp:8

PSA PAKE: invalid primitive type
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:9:exp:4:char*:"client":char*:"server":int:0:exp:10:exp:11

PSA PAKE: invalid primitive family
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:12:exp:4:char*:"client":char*:"server":int:0:exp:10:exp:11

PSA PAKE: invalid primitive bits
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:13:exp:4:char*:"client":char*:"server":int:0:exp:10:exp:11

PSA PAKE: invalid hash
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:14:char*:"client":char*:"server":int:0:exp:10:exp:11

PSA PAKE: duplicate a valid setup
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:15:exp:6

PSA PAKE: ecjpake setup role
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:16:exp:8

PSA PAKE: wrong password key type
depends_on:0:1:2
0:exp:0:exp:17:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:18:exp:8

PSA PAKE: wrong password key usage
depends_on:0:1:2
0:exp:0:exp:1:exp:19:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:18:exp:20

PSA PAKE: set empty user
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"":char*:"server":int:0:exp:21:exp:8

PSA PAKE: set empty peer
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"":int:0:exp:22:exp:8

PSA PAKE: user already set
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:23:exp:6

PSA PAKE: peer already set
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:24:exp:6

PSA PAKE: user and peer both servers  !!OM-PCI-26 -> PSA_ERROR_INVALID_ARGUMENT
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"server":char*:"server":int:0:exp:10:exp:8

PSA PAKE: user and peer both clients  !!OM-PCI-26 -> PSA_ERROR_INVALID_ARGUMENT
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"client":int:0:exp:10:exp:8

PSA PAKE: invalid input
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:1:exp:25:exp:8

PSA PAKE: unknown input step
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:1:exp:26:exp:8

PSA PAKE: invalid first input step
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:1:exp:27:exp:6

PSA PAKE: input buffer too large #1
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:1:exp:28:exp:8

PSA PAKE: input buffer too large #2  !!OM-PCI-26 -> PSA_ERROR_BAD_STATE
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:1:exp:29:exp:6

PSA PAKE: invalid output  !!OM-PCI-26 -> PSA_ERROR_BAD_STATE
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:25:exp:6

PSA PAKE: unknown output step
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:26:exp:8

PSA PAKE: invalid first output step
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:27:exp:6

PSA PAKE: output buffer too small
depends_on:0:1:2
0:exp:0:exp:1:exp:2:exp:3:exp:4:char*:"client":char*:"server":int:0:exp:28:exp:30

PSA PAKE: check rounds w/o forced errors
depends_on:0:1:2:3
2:exp:0:exp:3:exp:4:exp:31:hex:"abcdef":int:0:int:0:exp:32

PSA PAKE: check rounds w/o forced errors, TLS12_PRF
depends_on:0:1:2:4
2:exp:0:exp:3:exp:4:exp:33:hex:"abcdef":int:0:int:0:exp:32

PSA PAKE: check rounds, key is destroyed after being passed to set_password_key
depends_on:0:1:2:3
2:exp:0:exp:3:exp:4:exp:31:hex:"abcdef":int:0:int:1:exp:32

PSA PAKE: check rounds w/o forced errors, client input first
depends_on:0:1:2:3
2:exp:0:exp:3:exp:4:exp:31:hex:"abcdef":int:1:int:0:exp:32

PSA PAKE: force early key derivation 1
depends_on:0:1:2:3
2:exp:0:exp:3:exp:4:exp:31:hex:"abcdef":int:0:int:0:exp:34

PSA PAKE: force early key derivation 2
depends_on:0:1:2:3
2:exp:0:exp:3:exp:4:exp:31:hex:"abcdef":int:0:int:0:exp:35

PSA PAKE: no injected errors
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:32:exp:36:int:0

PSA PAKE: no injected errors, client input first
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:1:hex:"abcdef":exp:32:exp:36:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_CLIENT_KEY_SHARE_PART1
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:37:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_CLIENT_ZK_PUBLIC_PART1
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:39:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_CLIENT_ZK_PROOF_PART1
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:40:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_CLIENT_KEY_SHARE_PART2
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:41:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_CLIENT_ZK_PUBLIC_PART2
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:42:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_CLIENT_ZK_PROOF_PART2
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:43:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_SERVER_KEY_SHARE_PART1
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:44:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_SERVER_ZK_PUBLIC_PART1
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:45:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_SERVER_ZK_PROOF_PART1
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:46:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_SERVER_KEY_SHARE_PART2
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:47:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_SERVER_ZK_PUBLIC_PART2
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:48:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND1_SERVER_ZK_PROOF_PART2
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:49:exp:38:int:0

PSA PAKE: inject ERR_INJECT_ROUND2_CLIENT_KEY_SHARE
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:50:exp:38:int:1

PSA PAKE: inject ERR_INJECT_ROUND2_CLIENT_ZK_PUBLIC
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:51:exp:38:int:1

PSA PAKE: inject ERR_INJECT_ROUND2_CLIENT_ZK_PROOF
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:52:exp:38:int:1

PSA PAKE: inject ERR_INJECT_ROUND2_SERVER_KEY_SHARE
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:53:exp:38:int:1

PSA PAKE: inject ERR_INJECT_ROUND2_SERVER_ZK_PUBLIC
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:54:exp:38:int:1

PSA PAKE: inject ERR_INJECT_ROUND2_SERVER_ZK_PROOF
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:55:exp:38:int:1

PSA PAKE: inject ERR_INJECT_EXTRA_OUTPUT
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:56:exp:6:int:0

PSA PAKE: inject ERR_INJECT_EXTRA_INPUT
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:1:hex:"abcdef":exp:57:exp:6:int:0

PSA PAKE: inject ERR_INJECT_EXTRA_OUTPUT_AT_END
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:1:hex:"abcdef":exp:58:exp:6:int:1

PSA PAKE: inject ERR_INJECT_EXTRA_INPUT_AT_END
depends_on:0:1:2
1:exp:0:exp:3:exp:4:int:0:hex:"abcdef":exp:59:exp:6:int:1

PSA PAKE: ecjpake size macros
depends_on:0:1
3

PSA PAKE: input getters: password
4

PSA PAKE: input getters: cipher suite
5

PSA PAKE: input getters: user
6

PSA PAKE: input getters: peer
7

