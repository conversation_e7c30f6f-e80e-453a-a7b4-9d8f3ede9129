PSA storage read: AES-GCM+CTR
depends_on:0:1:2
1:exp:0:exp:1:int:128:exp:2:exp:3:exp:4:hex:"404142434445464748494a4b4c4d4e4f":hex:"505341004b45590000000000010000000024800001010000000250050010c00410000000404142434445464748494a4b4c4d4e4f":int:1

PSA storage save: AES-GCM+CTR
depends_on:0
0:exp:0:exp:1:int:128:exp:2:exp:3:exp:4:hex:"404142434445464748494a4b4c4d4e4f":hex:"505341004b45590000000000010000000024800001010000000250050010c00410000000404142434445464748494a4b4c4d4e4f"

PSA storage read: key larger than MBEDTLS_PSA_STATIC_KEY_SLOT_BUFFER_SIZE
depends_on:3:4
1:exp:0:exp:5:exp:6:exp:7:exp:8:exp:8:hex:"":hex:"":exp:9

