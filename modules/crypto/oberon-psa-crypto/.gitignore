# Random seed file created by test scripts and sample programs
seedfile

# CMake build artifacts:
CMakeCache.txt
CMakeFiles
CTestTestfile.cmake
cmake_install.cmake
Testing
# CMake generates *.dir/ folders for in-tree builds (used by MSVC projects), ignore all of those:
*.dir/
# MSVC files generated by CMake:
/*.sln
/*.vcxproj
/*.filters

# Test coverage build artifacts:
Coverage
*.gcno
*.gcda
coverage-summary.txt

# generated by scripts/memory.sh
massif-*

# MSVC build artifacts:
*.exe
*.pdb
*.ilk
*.lib

# Python build artifacts:
*.pyc

# Microsoft CMake extension for Visual Studio Code generates a build directory by default
build/
build_Test/

# Generated documentation:
/apidoc
/docs

# PSA Crypto compliance test repo, cloned by test_psa_compliance.py
#/psa-arch-tests # !!OM

# Editor navigation files:
/GPATH
/GRTAGS
/GSYMS
/GTAGS
/TAGS
/cscope*.out
/tags

# CLion
.idea/*
cmake-build-debug

# MacOS
.DS_Store

# Generated documentation
.pdf

# Visual Studio Code
.vscode/*
export/*
