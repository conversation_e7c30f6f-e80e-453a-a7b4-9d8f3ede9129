# oberon Directory Overview

This directory contains documentation, source code of the _Oberon drivers_, and
platform-specific _crypto configuration_ and mock _crypto driver_ examples.

- `docs`
This directory contains documentation for developers and system integrators, see
[Documentation Overview](docs/Documentation_Overview.md).

- `drivers`
This directory contains platform-agnostic _Oberon drivers_ that implement
cryptographic operations, most of them using the _ocrypto_ library.

- `platforms`
This directory contains platform-specific configuration examples and example
(mock) _hardware drivers_. The pseudo-platform _demo_ shows several
configurations that use _Oberon drivers_ and example _hardware drivers_.
